import { Component, EventEmitter, Inject, inject, Input, Output, PLATFORM_ID, effect, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from "@angular/router";
import { TranslocoService } from "@jsverse/transloco";
import { ContentService } from "@/services/content.service";
import { ToasterService } from '@/services/toaster.service';
import { LibraryService } from '@/services/library.service';
import { ProfileService } from "@/services/profile.service";
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { AuthService } from '@/services/auth.service';
import {environment} from "@/env/environment";
import { ClickOutsideDirective } from "@/directives/clickOutside";

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [RouterLink, CommonModule, NgOptimizedImage, ClickOutsideDirective],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent {
  // searchShow: boolean = false;
  @Output() sideOpen = new EventEmitter<any>();
  @Output() sideClose = new EventEmitter<any>();
  @Input() isSidebarOpen = false;
  route = inject(ActivatedRoute)
  router = inject(Router);
  contentService = inject(ContentService);
  libraryService = inject(LibraryService);
  translocoService = inject(TranslocoService);
  profileService = inject(ProfileService);
  authService = inject(AuthService);
  currentLanguage = signal<string>('ru');
  environment = environment;
  isUserMenuOpen = signal<boolean>(false);

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private toasterService: ToasterService
  ) {
    effect(() => {
      if (this.authService.token() && !this.profileService.name()) {
        this.profileService.getProfile().subscribe();
      } 
    });
   }

  ngOnInit() {
    // this.contentService.getAll().subscribe();
    // this.libraryService.getAll().subscribe();
  }

  // toggleSearchPanel() {
  //   this.searchShow = !this.searchShow;
  // }

  async changeLanguage(e: any) {
    const currentUrl = this.router.url
    const newUrl = currentUrl.replace(`/${this.translocoService.getActiveLang()}/`, `/${e.target.value}/`)
    this.router.navigate([newUrl])
  }

  toggleSidebar() {
    this.sideOpen.emit(true);
  }

  navigateToPhoto() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/photo`]);
    this.sideOpen.emit(false);
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
    this.sideOpen.emit(false);
  }

  navigateToForum() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/forum`]);
    this.sideOpen.emit(false);
  }


  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
    this.sideOpen.emit(false);
  }

  navigateToMain() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/`]);
    this.sideOpen.emit(false);
  }

  navigateToCatigory() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/categories`]);
    this.sideOpen.emit(false);
  }

  toggleUserMenu(event: Event) {
    event.stopPropagation();
    this.isUserMenuOpen.set(!this.isUserMenuOpen());
  }

  closeUserMenu() {
    this.isUserMenuOpen.set(false);
  }

  navigateToProfile() {
    this.router.navigate(['/ru/profile']);
    this.closeUserMenu();
    this.sideClose.emit(false);
  }

  logout() {
    this.authService.logout();
    this.closeUserMenu();
    this.sideClose.emit(false);
  }
}
